'use client';

import React, { useState, useEffect } from 'react';
import AdminDashboardLayout from '@/components/navigation/AdminDashboardLayout';
import withAdminAuth from '@/components/withAdminAuth';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  BuildingStorefrontIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  UserCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import {
  ProfileLayout,
  ProfileSection,
  ProfileCard
} from '@/components/ui/ProfileLayout';

interface CremationCenter {
  id: number | string;
  name: string;
  owner: string;
  email: string;
  phone: string;
  address: string;
  registrationDate: string;
  status: string;
  activeServices: number;
  totalBookings: number;
  revenue: string;
  rating?: number;
  description: string;
  verified: boolean;
  application_status: string;
  verification_status?: string;
  city?: string;
  province?: string;
}

function AdminCremationCentersPage({ adminData }: { adminData: any }) {
  const userName = adminData?.full_name || 'System Administrator';
  const [cremationCenters, setCremationCenters] = useState<CremationCenter[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const { showToast } = useToast();

  useEffect(() => {
    fetchCremationCenters();
  }, []);

  const fetchCremationCenters = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/admin/cremation-businesses', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Cremation centers API response:', data);

      if (data.success) {
        setCremationCenters(data.businesses || []);
      } else {
        console.error('Cremation centers API error:', data.error);
        showToast(data.error || 'Failed to fetch cremation centers', 'error');
      }
    } catch (error) {
      console.error('Error fetching cremation centers:', error);
      showToast(`Failed to fetch cremation centers: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const filteredCenters = cremationCenters.filter(center => {
    const matchesSearch = searchTerm === '' ||
      center.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      center.owner?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      center.address?.toLowerCase().includes(searchTerm.toLowerCase());

    const isRestricted = center.application_status === 'restricted' || center.verification_status === 'restricted' || center.status === 'restricted';
    const isVerified = center.verified === true || center.application_status === 'approved' || center.application_status === 'verified';
    const actualStatus = isRestricted ? 'restricted' : (isVerified ? 'active' : 'pending');

    const matchesStatus = statusFilter === 'all' || actualStatus === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (center: CremationCenter) => {
    const isRestricted = center.application_status === 'restricted' || center.verification_status === 'restricted' || center.status === 'restricted';
    const isVerified = center.verified === true || center.application_status === 'approved' || center.application_status === 'verified';

    if (isRestricted) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <ShieldExclamationIcon className="h-3 w-3 mr-1" />
          Restricted
        </span>
      );
    } else if (isVerified) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <ShieldCheckIcon className="h-3 w-3 mr-1" />
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          Pending
        </span>
      );
    }
  };

  return (
    <AdminDashboardLayout activePage="users" userName={userName}>
      <ProfileLayout
        title="Cremation Centers"
        subtitle="Manage cremation center registrations and verifications"
        icon={<BuildingStorefrontIcon className="h-8 w-8 text-white" />}
        showSkeleton={loading}
      >
        {/* Search and Filter Section */}
        <ProfileSection
          title="Search and Filter"
          subtitle="Find specific cremation centers using search and status filters"
          showSkeleton={loading}
          action={
            <button
              onClick={fetchCremationCenters}
              className="flex items-center px-4 py-2 bg-[var(--primary-green)] text-white rounded-lg hover:bg-[var(--primary-green-hover)] transition-colors"
              disabled={loading}
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          }
        >
          <ProfileCard>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              <div className="relative flex-grow sm:max-w-xs">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search cremation centers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                />
              </div>
              <div className="relative flex-grow sm:max-w-xs">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FunnelIcon className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm appearance-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="restricted">Restricted</option>
                </select>
              </div>
            </div>
          </ProfileCard>
        </ProfileSection>

        {/* Cremation Centers List Section */}
        <ProfileSection
          title="Cremation Centers"
          subtitle={`${filteredCenters.length} ${filteredCenters.length === 1 ? 'center' : 'centers'} found`}
          showSkeleton={loading}
        >
          <ProfileCard>
            {filteredCenters.length === 0 ? (
              <div className="text-center py-12">
                <BuildingStorefrontIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No cremation centers found</h3>
                <p className="text-gray-500">
                  {searchTerm || statusFilter !== 'all'
                    ? 'No cremation centers match your search criteria.'
                    : 'No cremation centers have been registered yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredCenters.map((center) => (
                  <div key={center.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0">
                      <div className="h-12 w-12 bg-[var(--primary-green)] rounded-lg flex items-center justify-center">
                        <BuildingStorefrontIcon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium text-gray-900">{center.name}</h3>
                        {getStatusBadge(center)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <UserCircleIcon className="h-4 w-4" />
                          <span>{center.owner}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <EnvelopeIcon className="h-4 w-4" />
                          <span>{center.email}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <PhoneIcon className="h-4 w-4" />
                          <span>{center.phone}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPinIcon className="h-4 w-4" />
                          <span className="truncate">{center.address}</span>
                        </div>
                        <div>
                          <span className="font-medium">Services:</span> {center.activeServices || 0}
                        </div>
                        <div>
                          <span className="font-medium">Bookings:</span> {center.totalBookings || 0}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ProfileCard>
        </ProfileSection>
      </ProfileLayout>
    </AdminDashboardLayout>
  );
}

export default withAdminAuth(AdminCremationCentersPage);