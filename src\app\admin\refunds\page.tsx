'use client';

import React, { useState, useEffect } from 'react';
import AdminDashboardLayout from '@/components/navigation/AdminDashboardLayout';
import withAdminAuth from '@/components/withAdminAuth';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  EyeIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import {
  ProfileLayout,
  ProfileSection,
  ProfileCard
} from '@/components/ui/ProfileLayout';

interface Refund {
  id: number;
  booking_id: number;
  amount: number | string;
  reason: string;
  status: string;
  processed_by?: number;
  payment_method?: string;
  transaction_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  user_name?: string;
  user_email?: string;
  service_name?: string;
  pet_name?: string;
  booking_date?: string;
  booking_time?: string;
}

function AdminRefundsPage({ adminData }: { adminData: any }) {
  const userName = adminData?.full_name || 'System Administrator';
  const [refunds, setRefunds] = useState<Refund[]>([]);
  const [filteredRefunds, setFilteredRefunds] = useState<Refund[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const { showToast } = useToast();

  useEffect(() => {
    fetchRefunds();
  }, []);

  useEffect(() => {
    filterRefunds();
  }, [refunds, searchTerm, statusFilter]);

  const fetchRefunds = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/admin/refunds', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Refunds API response:', data);

      if (data.success) {
        setRefunds(data.refunds || []);
      } else {
        console.error('Refunds API error:', data.error);
        showToast(data.error || 'Failed to fetch refunds', 'error');
      }
    } catch (error) {
      console.error('Error fetching refunds:', error);
      showToast(`Failed to fetch refunds: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const filterRefunds = () => {
    let filtered = refunds;

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(refund =>
        refund.user_name?.toLowerCase().includes(term) ||
        refund.user_email?.toLowerCase().includes(term) ||
        refund.pet_name?.toLowerCase().includes(term) ||
        refund.reason.toLowerCase().includes(term) ||
        refund.booking_id.toString().includes(term)
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(refund => refund.status === statusFilter);
    }

    setFilteredRefunds(filtered);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending Review' },
      processing: { color: 'bg-blue-100 text-blue-800', label: 'Processing' },
      processed: { color: 'bg-green-100 text-green-800', label: 'Completed' },
      failed: { color: 'bg-red-100 text-red-800', label: 'Failed' },
      cancelled: { color: 'bg-gray-100 text-gray-800', label: 'Denied' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <AdminDashboardLayout activePage="refunds" userName={userName}>
      <ProfileLayout
        title="Refund Management"
        subtitle="Process and manage customer refund requests"
        icon={<CurrencyDollarIcon className="h-8 w-8 text-white" />}
        showSkeleton={loading}
      >
        {/* Search and Filter Section */}
        <ProfileSection
          title="Search and Filter"
          subtitle="Find specific refund requests using search and status filters"
          showSkeleton={loading}
          action={
            <button
              onClick={fetchRefunds}
              className="flex items-center px-4 py-2 bg-[var(--primary-green)] text-white rounded-lg hover:bg-[var(--primary-green-hover)] transition-colors"
              disabled={loading}
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          }
        >
          <ProfileCard>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              <div className="relative flex-grow sm:max-w-xs">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search refunds..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                />
              </div>
              <div className="relative flex-grow sm:max-w-xs">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FunnelIcon className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm appearance-none"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending Review</option>
                  <option value="processing">Processing</option>
                  <option value="processed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="cancelled">Denied</option>
                </select>
              </div>
            </div>
          </ProfileCard>
        </ProfileSection>

        {/* Refunds List Section */}
        <ProfileSection
          title="Refund Requests"
          subtitle={`${filteredRefunds.length} ${filteredRefunds.length === 1 ? 'refund' : 'refunds'} found`}
          showSkeleton={loading}
        >
          <ProfileCard>
            {filteredRefunds.length === 0 ? (
              <div className="text-center py-12">
                <CurrencyDollarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No refunds found</h3>
                <p className="text-gray-500">
                  {searchTerm || statusFilter !== 'all'
                    ? 'No refunds match your search criteria.'
                    : 'No refund requests have been submitted yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredRefunds.map((refund) => (
                  <div key={refund.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium text-gray-900">
                          Booking #{refund.booking_id}
                        </h3>
                        {getStatusBadge(refund.status)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Customer:</span> {refund.user_name}
                        </div>
                        <div>
                          <span className="font-medium">Pet:</span> {refund.pet_name}
                        </div>
                        <div>
                          <span className="font-medium">Amount:</span> ₱{parseFloat(refund.amount.toString()).toFixed(2)}
                        </div>
                        <div>
                          <span className="font-medium">Requested:</span> {new Date(refund.created_at).toLocaleDateString()}
                        </div>
                      </div>
                      <p className="mt-2 text-sm text-gray-700">
                        <span className="font-medium">Reason:</span> {refund.reason}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <button className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ProfileCard>
        </ProfileSection>
      </ProfileLayout>
    </AdminDashboardLayout>
  );
}

export default withAdminAuth(AdminRefundsPage);
