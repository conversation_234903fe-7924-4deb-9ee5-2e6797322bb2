'use client';

import React, { useState, useEffect } from 'react';
import AdminDashboardLayout from '@/components/navigation/AdminDashboardLayout';
import withAdminAuth from '@/components/withAdminAuth';
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon,
  UserIcon,
  ClockIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import {
  ProfileLayout,
  ProfileSection,
  ProfileCard
} from '@/components/ui/ProfileLayout';

interface AdminLog {
  id: number;
  admin_id: number;
  admin_name: string;
  admin_username: string;
  action: string;
  entity_type: string;
  entity_id: number;
  details: string | object;
  created_at: string;
}

function AdminLogsPage({ adminData }: { adminData: any }) {
  const userName = adminData?.full_name || 'System Administrator';
  const [logs, setLogs] = useState<AdminLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const { showToast } = useToast();

  useEffect(() => {
    fetchLogs();
  }, []);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      console.log('Fetching logs...');

      const response = await fetch('/api/admin/logs?page=1&limit=50', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      console.log('Logs API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Logs API error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Logs API response data:', data);

      if (data.success) {
        setLogs(data.logs || []);
        console.log('Successfully loaded logs:', data.logs?.length || 0);
      } else {
        console.error('Logs API error:', data.error);
        showToast(data.error || 'Failed to fetch logs', 'error');
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
      showToast(`Failed to fetch logs: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = searchTerm === '' ||
      log.admin_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.entity_type?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesAction = actionFilter === 'all' || log.action?.toLowerCase().includes(actionFilter.toLowerCase());

    return matchesSearch && matchesAction;
  });

  const getActionIcon = (action: string) => {
    const actionLower = action.toLowerCase();
    if (actionLower.includes('approve')) return CheckCircleIcon;
    if (actionLower.includes('reject') || actionLower.includes('restrict')) return XCircleIcon;
    if (actionLower.includes('login')) return UserIcon;
    return DocumentTextIcon;
  };

  const getActionColor = (action: string) => {
    const actionLower = action.toLowerCase();
    if (actionLower.includes('approve')) return 'text-green-600 bg-green-100';
    if (actionLower.includes('reject') || actionLower.includes('restrict')) return 'text-red-600 bg-red-100';
    if (actionLower.includes('login')) return 'text-purple-600 bg-purple-100';
    return 'text-gray-600 bg-gray-100';
  };

  return (
    <AdminDashboardLayout activePage="logs" userName={userName}>
      <ProfileLayout
        title="Activity Monitor"
        subtitle="Track admin activities and business operations"
        icon={<DocumentTextIcon className="h-8 w-8 text-white" />}
        showSkeleton={loading}
      >
        {/* Search and Filter Section */}
        <ProfileSection
          title="Activity Filters"
          subtitle="Filter activities by type, admin, and search criteria"
          showSkeleton={loading}
          action={
            <button
              onClick={fetchLogs}
              className="flex items-center px-4 py-2 bg-[var(--primary-green)] text-white rounded-lg hover:bg-[var(--primary-green-hover)] transition-colors"
              disabled={loading}
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          }
        >
          <ProfileCard>
            {loading ? (
              /* Loading skeleton that matches actual search/filter layout */
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                {/* Search input skeleton */}
                <div className="relative flex-grow sm:max-w-xs">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="h-10 bg-gray-200 rounded-lg w-full animate-pulse"></div>
                </div>
                {/* Filter dropdown skeleton */}
                <div className="relative flex-grow sm:max-w-xs">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="h-10 bg-gray-200 rounded-lg w-full animate-pulse"></div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                <div className="relative flex-grow sm:max-w-xs">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search logs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                  />
                </div>
                <div className="relative flex-grow sm:max-w-xs">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FunnelIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    value={actionFilter}
                    onChange={(e) => setActionFilter(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm appearance-none"
                  >
                    <option value="all">All Activities</option>
                    <option value="approve">Approvals</option>
                    <option value="reject">Rejections</option>
                    <option value="restrict">Restrictions</option>
                    <option value="login">Admin Logins</option>
                  </select>
                </div>
              </div>
            )}
          </ProfileCard>
        </ProfileSection>

        {/* Activity Logs Section */}
        <ProfileSection
          title="Activity Logs"
          subtitle={`${filteredLogs.length} ${filteredLogs.length === 1 ? 'activity' : 'activities'} recorded`}
          showSkeleton={loading}
        >
          <ProfileCard>
            {loading ? (
              /* Loading skeleton that matches actual activity logs layout */
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    {/* Action icon skeleton */}
                    <div className="p-2 rounded-full bg-gray-200 animate-pulse">
                      <div className="h-5 w-5 bg-gray-300 rounded animate-pulse"></div>
                    </div>
                    {/* Content skeleton */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                      </div>
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center space-x-1">
                          <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-28 animate-pulse"></div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-32 animate-pulse"></div>
                        </div>
                      </div>
                    </div>
                    {/* Action button skeleton */}
                    <div className="px-3 py-2 bg-gray-200 rounded-lg animate-pulse">
                      <div className="h-4 w-16 bg-gray-300 rounded animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredLogs.length === 0 ? (
              <div className="text-center py-12">
                <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No activities found</h3>
                <p className="text-gray-500">
                  {searchTerm || actionFilter !== 'all'
                    ? 'No activities match your current filters.'
                    : 'No admin activities have been recorded yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredLogs.map((log) => {
                  const ActionIcon = getActionIcon(log.action);
                  const actionColor = getActionColor(log.action);

                  return (
                    <div key={log.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className={`p-2 rounded-full ${actionColor}`}>
                        <ActionIcon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-medium text-gray-900">
                            {log.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </h3>
                          <span className="text-sm text-gray-500">
                            on {log.entity_type.replace(/_/g, ' ')} #{log.entity_id}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <UserIcon className="h-4 w-4" />
                            <span>{log.admin_name} ({log.admin_username})</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <ClockIcon className="h-4 w-4" />
                            <span>{new Date(log.created_at).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      <button className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        Details
                      </button>
                    </div>
                  );
                })}
              </div>
            )}
          </ProfileCard>
        </ProfileSection>
      </ProfileLayout>
    </AdminDashboardLayout>
  );
}

export default withAdminAuth(AdminLogsPage);
